/**
 * @jest-environment jsdom
 */

import React from 'react';
import { screen, waitFor, fireEvent, within, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import { renderWithProviders } from '../../../utils/donationTestUtils';

// Mock axios for API calls
jest.mock('axios');
const mockedAxios = axios;

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    pathname: '/donation-head',
    query: {},
    asPath: '/donation-head',
    route: '/donation-head',
    events: {
      on: jest.fn(),
      off: jest.fn(),
      emit: jest.fn(),
    },
  }),
}));

// Mock Material-UI DataGrid
jest.mock('@mui/x-data-grid', () => ({
  DataGrid: ({ rows, columns, onRowClick, loading, ...props }) => (
    <div data-testid="data-grid" {...props}>
      {loading ? (
        <div data-testid="loading-indicator">Loading...</div>
      ) : (
        <div>
          {rows?.map((row, index) => (
            <div
              key={row.id || index}
              data-testid={`row-${row.id || index}`}
              onClick={() => onRowClick && onRowClick({ row })}
              style={{ cursor: 'pointer', padding: '8px', border: '1px solid #ccc', margin: '2px' }}
            >
              <span data-testid={`row-name-${row.id || index}`}>{row.name}</span>
              <span data-testid={`row-status-${row.id || index}`}>
                {row.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  ),
  GridToolbar: () => <div data-testid="grid-toolbar">Grid Toolbar</div>,
}));

// Integration Test Component that simulates the full donation-head page
const IntegratedDonationHeadPage = () => {
  const [donationHeads, setDonationHeads] = React.useState([]);
  const [loading, setLoading] = React.useState(true);
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [selectedHead, setSelectedHead] = React.useState(null);
  const [searchTerm, setSearchTerm] = React.useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [advancedSearchOpen, setAdvancedSearchOpen] = React.useState(false);

  // Simulate API calls
  const fetchDonationHeads = React.useCallback(async () => {
    setLoading(true);
    try {
      const response = await mockedAxios.get('/api/donation-heads');
      act(() => {
        setDonationHeads(response.data || []);
      });
    } catch (error) {
      console.error('Failed to fetch donation heads:', error);
      act(() => {
        setDonationHeads([]);
      });
    } finally {
      act(() => {
        setLoading(false);
      });
    }
  }, []);

  const createDonationHead = async (data) => {
    try {
      const response = await mockedAxios.post('/api/donation-heads', data);
      act(() => {
        setDonationHeads(prev => [...prev, response.data]);
        setDialogOpen(false);
      });
      return response.data;
    } catch (error) {
      throw new Error('Failed to create donation head');
    }
  };

  const updateDonationHead = async (id, data) => {
    try {
      const response = await mockedAxios.put(`/api/donation-heads/${id}`, data);
      act(() => {
        setDonationHeads(prev => prev.map(head => head.id === id ? response.data : head));
        setDialogOpen(false);
      });
      return response.data;
    } catch (error) {
      throw new Error('Failed to update donation head');
    }
  };

  const deleteDonationHead = async (id) => {
    try {
      await mockedAxios.delete(`/api/donation-heads/${id}`);
      act(() => {
        setDonationHeads(prev => prev.filter(head => head.id !== id));
        setDeleteDialogOpen(false);
      });
    } catch (error) {
      throw new Error('Failed to delete donation head');
    }
  };

  React.useEffect(() => {
    fetchDonationHeads();
  }, [fetchDonationHeads]);

  const filteredHeads = donationHeads.filter(head =>
    head.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleRowClick = (params) => {
    setSelectedHead(params.row);
    setDialogOpen(true);
  };

  const handleCreateNew = () => {
    setSelectedHead(null);
    setDialogOpen(true);
  };

  const handleDelete = (head) => {
    setSelectedHead(head);
    setDeleteDialogOpen(true);
  };

  return (
    <div data-testid="donation-head-page">
      <h1>Donation Head Management</h1>
      
      {/* Search and Actions */}
      <div data-testid="page-header">
        <input
          type="text"
          placeholder="Search donation heads..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          data-testid="search-input"
        />
        <button onClick={handleCreateNew} data-testid="create-button">
          Create New
        </button>
        <button 
          onClick={() => setAdvancedSearchOpen(true)} 
          data-testid="advanced-search-button"
        >
          Advanced Search
        </button>
        <button onClick={fetchDonationHeads} data-testid="refresh-button">
          Refresh
        </button>
      </div>

      {/* Data Grid */}
      <div data-testid="data-grid-container">
        <div data-testid="data-grid">
          {loading ? (
            <div data-testid="loading-indicator">Loading...</div>
          ) : (
            <div>
              {filteredHeads.map((head) => (
                <div
                  key={head.id}
                  data-testid={`row-${head.id}`}
                  onClick={() => handleRowClick({ row: head })}
                  style={{ cursor: 'pointer', padding: '8px', border: '1px solid #ccc', margin: '2px' }}
                >
                  <span data-testid={`row-name-${head.id}`}>{head.name}</span>
                  <span data-testid={`row-status-${head.id}`}>
                    {head.isActive ? 'Active' : 'Inactive'}
                  </span>
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete(head);
                    }}
                    data-testid={`delete-button-${head.id}`}
                  >
                    Delete
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Create/Edit Dialog */}
      {dialogOpen && (
        <div data-testid="donation-head-dialog">
          <h2>{selectedHead ? 'Edit Donation Head' : 'Create Donation Head'}</h2>
          <form
            onSubmit={async (e) => {
              e.preventDefault();
              const formData = new FormData(e.target);
              const data = {
                name: formData.get('name'),
                description: formData.get('description'),
                orgId: formData.get('orgId'),
                isActive: formData.get('isActive') === 'on',
              };
              
              try {
                if (selectedHead) {
                  await updateDonationHead(selectedHead.id, data);
                } else {
                  await createDonationHead(data);
                }
              } catch (error) {
                console.error('Form submission error:', error);
              }
            }}
          >
            <input
              name="name"
              placeholder="Name"
              defaultValue={selectedHead?.name || ''}
              data-testid="name-input"
              required
            />
            <textarea
              name="description"
              placeholder="Description"
              defaultValue={selectedHead?.description || ''}
              data-testid="description-input"
            />
            <input
              name="orgId"
              placeholder="Organization ID"
              defaultValue={selectedHead?.orgId || ''}
              data-testid="orgId-input"
              required
            />
            <label>
              <input
                type="checkbox"
                name="isActive"
                defaultChecked={selectedHead?.isActive ?? true}
                data-testid="isActive-checkbox"
              />
              Active
            </label>
            <button type="submit" data-testid="submit-button">
              {selectedHead ? 'Update' : 'Create'}
            </button>
            <button 
              type="button" 
              onClick={() => setDialogOpen(false)}
              data-testid="cancel-button"
            >
              Cancel
            </button>
          </form>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      {deleteDialogOpen && (
        <div data-testid="delete-dialog">
          <h2>Confirm Delete</h2>
          <p>Are you sure you want to delete "{selectedHead?.name}"?</p>
          <button 
            onClick={() => deleteDonationHead(selectedHead.id)}
            data-testid="confirm-delete-button"
          >
            Delete
          </button>
          <button 
            onClick={() => setDeleteDialogOpen(false)}
            data-testid="cancel-delete-button"
          >
            Cancel
          </button>
        </div>
      )}

      {/* Advanced Search Dialog */}
      {advancedSearchOpen && (
        <div data-testid="advanced-search-dialog">
          <h2>Advanced Search</h2>
          <form>
            <input placeholder="Name" data-testid="advanced-name-input" />
            <select data-testid="advanced-status-select">
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
            <input placeholder="Organization" data-testid="advanced-org-input" />
            <button type="submit" data-testid="advanced-search-submit">Search</button>
            <button 
              type="button" 
              onClick={() => setAdvancedSearchOpen(false)}
              data-testid="advanced-search-close"
            >
              Close
            </button>
          </form>
        </div>
      )}
    </div>
  );
};

describe('Donation Head Page - Integration Tests', () => {
  const user = userEvent.setup();

  // Mock data
  const mockDonationHeads = [
    {
      id: '1',
      name: 'Education Fund',
      description: 'Supporting education initiatives',
      orgId: 'org-1',
      isActive: true,
      createdOn: '2024-01-01T00:00:00Z',
      updatedOn: '2024-01-01T00:00:00Z',
    },
    {
      id: '2',
      name: 'Healthcare Support',
      description: 'Medical assistance program',
      orgId: 'org-1',
      isActive: false,
      createdOn: '2024-01-02T00:00:00Z',
      updatedOn: '2024-01-02T00:00:00Z',
    },
    {
      id: '3',
      name: 'Food Aid Program',
      description: 'Providing food assistance',
      orgId: 'org-2',
      isActive: true,
      createdOn: '2024-01-03T00:00:00Z',
      updatedOn: '2024-01-03T00:00:00Z',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default API responses
    mockedAxios.get.mockResolvedValue({ data: mockDonationHeads });
    mockedAxios.post.mockResolvedValue({ 
      data: { 
        id: '4', 
        name: 'New Donation Head', 
        description: 'New description',
        orgId: 'org-1',
        isActive: true,
        createdOn: new Date().toISOString(),
        updatedOn: new Date().toISOString(),
      } 
    });
    mockedAxios.put.mockResolvedValue({ 
      data: { 
        ...mockDonationHeads[0], 
        name: 'Updated Name',
        updatedOn: new Date().toISOString(),
      } 
    });
    mockedAxios.delete.mockResolvedValue({ data: {} });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Page Loading and Initial State', () => {
    it('should load the page and fetch donation heads on mount', async () => {
      renderWithProviders(<IntegratedDonationHeadPage />);

      // Should show loading initially
      expect(screen.getByTestId('loading-indicator')).toBeInTheDocument();

      // Should fetch data and display it
      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/donation-heads');
        expect(screen.getByText('Education Fund')).toBeInTheDocument();
        expect(screen.getByText('Healthcare Support')).toBeInTheDocument();
        expect(screen.getByText('Food Aid Program')).toBeInTheDocument();
      });

      // Should not show loading anymore
      expect(screen.queryByTestId('loading-indicator')).not.toBeInTheDocument();
    });

    it('should handle API errors gracefully', async () => {
      mockedAxios.get.mockRejectedValue(new Error('API Error'));

      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/donation-heads');
        expect(screen.queryByTestId('loading-indicator')).not.toBeInTheDocument();
      });

      // Should not crash and should show empty state
      expect(screen.getByTestId('data-grid')).toBeInTheDocument();
    });

    it('should display all page elements correctly', async () => {
      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(screen.getByText('Donation Head Management')).toBeInTheDocument();
        expect(screen.getByTestId('search-input')).toBeInTheDocument();
        expect(screen.getByTestId('create-button')).toBeInTheDocument();
        expect(screen.getByTestId('advanced-search-button')).toBeInTheDocument();
        expect(screen.getByTestId('refresh-button')).toBeInTheDocument();
        expect(screen.getByTestId('data-grid-container')).toBeInTheDocument();
      });
    });
  });

  describe('Search Functionality', () => {
    it('should filter donation heads based on search term', async () => {
      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(screen.getByText('Education Fund')).toBeInTheDocument();
        expect(screen.getByText('Healthcare Support')).toBeInTheDocument();
        expect(screen.getByText('Food Aid Program')).toBeInTheDocument();
      });

      // Search for "Education"
      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, 'Education');

      // Should only show Education Fund
      expect(screen.getByText('Education Fund')).toBeInTheDocument();
      expect(screen.queryByText('Healthcare Support')).not.toBeInTheDocument();
      expect(screen.queryByText('Food Aid Program')).not.toBeInTheDocument();
    });

    it('should show all items when search is cleared', async () => {
      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(screen.getByText('Education Fund')).toBeInTheDocument();
      });

      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, 'Education');

      // Clear search
      await user.clear(searchInput);

      // Should show all items again
      expect(screen.getByText('Education Fund')).toBeInTheDocument();
      expect(screen.getByText('Healthcare Support')).toBeInTheDocument();
      expect(screen.getByText('Food Aid Program')).toBeInTheDocument();
    });

    it('should open advanced search dialog', async () => {
      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(screen.getByTestId('advanced-search-button')).toBeInTheDocument();
      });

      const advancedSearchButton = screen.getByTestId('advanced-search-button');
      await user.click(advancedSearchButton);

      expect(screen.getByTestId('advanced-search-dialog')).toBeInTheDocument();
      expect(screen.getByText('Advanced Search')).toBeInTheDocument();
      expect(screen.getByTestId('advanced-name-input')).toBeInTheDocument();
      expect(screen.getByTestId('advanced-status-select')).toBeInTheDocument();
      expect(screen.getByTestId('advanced-org-input')).toBeInTheDocument();
    });
  });

  describe('CRUD Operations', () => {
    it('should create a new donation head successfully', async () => {
      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(screen.getByTestId('create-button')).toBeInTheDocument();
      });

      // Click create button
      const createButton = screen.getByTestId('create-button');
      await user.click(createButton);

      // Should open dialog
      expect(screen.getByTestId('donation-head-dialog')).toBeInTheDocument();
      expect(screen.getByText('Create Donation Head')).toBeInTheDocument();

      // Fill form
      await user.type(screen.getByTestId('name-input'), 'New Donation Head');
      await user.type(screen.getByTestId('description-input'), 'New description');
      await user.type(screen.getByTestId('orgId-input'), 'org-1');

      // Submit form
      const submitButton = screen.getByTestId('submit-button');
      await user.click(submitButton);

      // Should call API
      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledWith('/api/donation-heads', {
          name: 'New Donation Head',
          description: 'New description',
          orgId: 'org-1',
          isActive: true,
        });
      });

      // Dialog should close
      expect(screen.queryByTestId('donation-head-dialog')).not.toBeInTheDocument();
    });

    it('should edit an existing donation head successfully', async () => {
      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(screen.getByTestId('row-1')).toBeInTheDocument();
      });

      // Click on a row to edit
      const row = screen.getByTestId('row-1');
      await user.click(row);

      // Should open edit dialog
      expect(screen.getByTestId('donation-head-dialog')).toBeInTheDocument();
      expect(screen.getByText('Edit Donation Head')).toBeInTheDocument();

      // Form should be pre-filled
      expect(screen.getByTestId('name-input')).toHaveValue('Education Fund');

      // Update name
      const nameInput = screen.getByTestId('name-input');
      await user.clear(nameInput);
      await user.type(nameInput, 'Updated Name');

      // Submit form
      const submitButton = screen.getByTestId('submit-button');
      await user.click(submitButton);

      // Should call API
      await waitFor(() => {
        expect(mockedAxios.put).toHaveBeenCalledWith('/api/donation-heads/1', {
          name: 'Updated Name',
          description: 'Supporting education initiatives',
          orgId: 'org-1',
          isActive: true,
        });
      });

      // Dialog should close
      expect(screen.queryByTestId('donation-head-dialog')).not.toBeInTheDocument();
    });

    it('should delete a donation head successfully', async () => {
      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(screen.getByTestId('delete-button-1')).toBeInTheDocument();
      });

      // Click delete button
      const deleteButton = screen.getByTestId('delete-button-1');
      await user.click(deleteButton);

      // Should open delete confirmation dialog
      expect(screen.getByTestId('delete-dialog')).toBeInTheDocument();
      expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
      expect(screen.getByText('Are you sure you want to delete "Education Fund"?')).toBeInTheDocument();

      // Confirm delete
      const confirmDeleteButton = screen.getByTestId('confirm-delete-button');
      await user.click(confirmDeleteButton);

      // Should call API
      await waitFor(() => {
        expect(mockedAxios.delete).toHaveBeenCalledWith('/api/donation-heads/1');
      });

      // Dialog should close
      expect(screen.queryByTestId('delete-dialog')).not.toBeInTheDocument();
    });

    it('should cancel delete operation', async () => {
      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(screen.getByTestId('delete-button-1')).toBeInTheDocument();
      });

      // Click delete button
      const deleteButton = screen.getByTestId('delete-button-1');
      await user.click(deleteButton);

      // Should open delete confirmation dialog
      expect(screen.getByTestId('delete-dialog')).toBeInTheDocument();

      // Cancel delete
      const cancelDeleteButton = screen.getByTestId('cancel-delete-button');
      await user.click(cancelDeleteButton);

      // Should not call API
      expect(mockedAxios.delete).not.toHaveBeenCalled();

      // Dialog should close
      expect(screen.queryByTestId('delete-dialog')).not.toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle create operation errors', async () => {
      mockedAxios.post.mockRejectedValue(new Error('Create failed'));

      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(screen.getByTestId('create-button')).toBeInTheDocument();
      });

      // Open create dialog and fill form
      const createButton = screen.getByTestId('create-button');
      await user.click(createButton);

      await user.type(screen.getByTestId('name-input'), 'Test Name');
      await user.type(screen.getByTestId('orgId-input'), 'org-1');

      // Submit form
      const submitButton = screen.getByTestId('submit-button');
      await user.click(submitButton);

      // Should call API but handle error gracefully
      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalled();
      });

      // Dialog should remain open on error
      expect(screen.getByTestId('donation-head-dialog')).toBeInTheDocument();
    });

    it('should handle update operation errors', async () => {
      mockedAxios.put.mockRejectedValue(new Error('Update failed'));

      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(screen.getByTestId('row-1')).toBeInTheDocument();
      });

      // Click on row to edit
      const row = screen.getByTestId('row-1');
      await user.click(row);

      // Update and submit
      const nameInput = screen.getByTestId('name-input');
      await user.clear(nameInput);
      await user.type(nameInput, 'Updated Name');

      const submitButton = screen.getByTestId('submit-button');
      await user.click(submitButton);

      // Should call API but handle error gracefully
      await waitFor(() => {
        expect(mockedAxios.put).toHaveBeenCalled();
      });

      // Dialog should remain open on error
      expect(screen.getByTestId('donation-head-dialog')).toBeInTheDocument();
    });

    it('should handle delete operation errors', async () => {
      mockedAxios.delete.mockRejectedValue(new Error('Delete failed'));

      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(screen.getByTestId('delete-button-1')).toBeInTheDocument();
      });

      // Click delete and confirm
      const deleteButton = screen.getByTestId('delete-button-1');
      await user.click(deleteButton);

      const confirmDeleteButton = screen.getByTestId('confirm-delete-button');
      await user.click(confirmDeleteButton);

      // Should call API but handle error gracefully
      await waitFor(() => {
        expect(mockedAxios.delete).toHaveBeenCalled();
      });

      // Dialog should remain open on error
      expect(screen.getByTestId('delete-dialog')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should handle refresh functionality', async () => {
      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(screen.getByTestId('refresh-button')).toBeInTheDocument();
      });

      // Clear previous calls
      mockedAxios.get.mockClear();

      // Click refresh
      const refreshButton = screen.getByTestId('refresh-button');
      await user.click(refreshButton);

      // Should call API again
      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/donation-heads');
      });
    });

    it('should handle dialog cancellation', async () => {
      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(screen.getByTestId('create-button')).toBeInTheDocument();
      });

      // Open create dialog
      const createButton = screen.getByTestId('create-button');
      await user.click(createButton);

      expect(screen.getByTestId('donation-head-dialog')).toBeInTheDocument();

      // Cancel dialog
      const cancelButton = screen.getByTestId('cancel-button');
      await user.click(cancelButton);

      // Dialog should close
      expect(screen.queryByTestId('donation-head-dialog')).not.toBeInTheDocument();
    });

    it('should handle advanced search dialog interactions', async () => {
      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(screen.getByTestId('advanced-search-button')).toBeInTheDocument();
      });

      // Open advanced search
      const advancedSearchButton = screen.getByTestId('advanced-search-button');
      await user.click(advancedSearchButton);

      expect(screen.getByTestId('advanced-search-dialog')).toBeInTheDocument();

      // Fill advanced search form
      await user.type(screen.getByTestId('advanced-name-input'), 'Education');
      await user.selectOptions(screen.getByTestId('advanced-status-select'), 'active');
      await user.type(screen.getByTestId('advanced-org-input'), 'org-1');

      // Close dialog
      const closeButton = screen.getByTestId('advanced-search-close');
      await user.click(closeButton);

      expect(screen.queryByTestId('advanced-search-dialog')).not.toBeInTheDocument();
    });
  });

  describe('Data Validation', () => {
    it('should validate required fields in create form', async () => {
      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(screen.getByTestId('create-button')).toBeInTheDocument();
      });

      // Open create dialog
      const createButton = screen.getByTestId('create-button');
      await user.click(createButton);

      // Try to submit without required fields
      const submitButton = screen.getByTestId('submit-button');
      await user.click(submitButton);

      // Form should not submit (HTML5 validation)
      expect(mockedAxios.post).not.toHaveBeenCalled();
    });

    it('should handle checkbox state correctly', async () => {
      renderWithProviders(<IntegratedDonationHeadPage />);

      await waitFor(() => {
        expect(screen.getByTestId('create-button')).toBeInTheDocument();
      });

      // Open create dialog
      const createButton = screen.getByTestId('create-button');
      await user.click(createButton);

      // Check that isActive checkbox is checked by default
      const checkbox = screen.getByTestId('isActive-checkbox');
      expect(checkbox).toBeChecked();

      // Uncheck it
      await user.click(checkbox);
      expect(checkbox).not.toBeChecked();

      // Fill required fields and submit
      await user.type(screen.getByTestId('name-input'), 'Test Name');
      await user.type(screen.getByTestId('orgId-input'), 'org-1');

      const submitButton = screen.getByTestId('submit-button');
      await user.click(submitButton);

      // Should submit with isActive: false
      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledWith('/api/donation-heads', {
          name: 'Test Name',
          description: '',
          orgId: 'org-1',
          isActive: false,
        });
      });
    });
  });
});
